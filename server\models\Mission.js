const mongoose = require('mongoose');

const missionSchema = new mongoose.Schema({
  missionId: {
    type: String,
    required: true,
    unique: true
  },
  droneId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Drone',
    required: true
  },
  organizationId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'OrganizationRegistration',
    required: false
  },
  individualId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'IndividualRegistration',
    required: false
  },
  missionType: {
    type: String,
    enum: ['Spraying', 'Surveillance', 'Mapping', 'Delivery', 'Other'],
    default: 'Spraying'
  },
  status: {
    type: String,
    enum: ['Planned', 'In Progress', 'Completed', 'Cancelled', 'Failed'],
    default: 'Planned'
  },
  startTime: {
    type: Date,
    required: true
  },
  endTime: {
    type: Date
  },
  location: {
    startPoint: {
      latitude: { type: Number, required: true },
      longitude: { type: Number, required: true },
      altitude: { type: Number, default: 0 }
    },
    endPoint: {
      latitude: { type: Number },
      longitude: { type: Number },
      altitude: { type: Number, default: 0 }
    },
    area: {
      type: String, // GeoJSON polygon or area description
      required: false
    },
    state: {
      type: String,
      required: true
    },
    district: {
      type: String,
      required: false
    },
    village: {
      type: String,
      required: false
    }
  },
  sprayingDetails: {
    areaSprayedHectares: {
      type: Number,
      default: 0,
      min: 0
    },
    cropType: {
      type: String,
      required: false
    },
    pesticide: {
      name: String,
      quantity: Number,
      unit: String
    },
    weatherConditions: {
      temperature: Number,
      humidity: Number,
      windSpeed: Number,
      windDirection: String
    }
  },
  flightPath: [{
    timestamp: Date,
    latitude: Number,
    longitude: Number,
    altitude: Number,
    batteryLevel: Number,
    speed: Number
  }],
  notes: {
    type: String,
    maxlength: 1000
  },
  images: [{
    url: String,
    timestamp: Date,
    location: {
      latitude: Number,
      longitude: Number
    }
  }]
}, { timestamps: true });

// Validation to ensure either organizationId or individualId is provided
missionSchema.pre('save', function(next) {
  if (!this.organizationId && !this.individualId) {
    return next(new Error('Either organizationId or individualId must be provided'));
  }
  if (this.organizationId && this.individualId) {
    return next(new Error('Cannot have both organizationId and individualId'));
  }
  next();
});

// Index for better query performance
missionSchema.index({ organizationId: 1, createdAt: -1 });
missionSchema.index({ individualId: 1, createdAt: -1 });
missionSchema.index({ droneId: 1, createdAt: -1 });
missionSchema.index({ 'location.state': 1, createdAt: -1 });
missionSchema.index({ status: 1, createdAt: -1 });

module.exports = mongoose.model('Mission', missionSchema);

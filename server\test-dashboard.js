const mongoose = require('mongoose');
const dotenv = require('dotenv');
const bcrypt = require('bcryptjs');

// Load environment variables
dotenv.config();

// Import models
const Organization = require('./models/organizationModel');
const Individual = require('./models/IndividualRegistration');
const OrganizationLogin = require('./models/organizationLoginModel');
const Drone = require('./models/Drone');
const Mission = require('./models/Mission');

// Connect to database
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/shakti-dev');
    console.log('✅ MongoDB connected for testing');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Create test data
const createTestData = async () => {
  try {
    console.log('🔄 Creating test data...');

    // Clean existing test data
    await Organization.deleteMany({ orgName: /Test Organization/ });
    await Individual.deleteMany({ 'personalDetails.name': /Test Individual/ });
    await OrganizationLogin.deleteMany({ username: /test/ });
    await Drone.deleteMany({ droneId: /TEST/ });
    await Mission.deleteMany({ missionId: /TEST/ });

    // Create test organization
    const testOrg = await Organization.create({
      orgName: 'Test Organization Ltd',
      orgType: 'Private',
      registrationNumber: 'TEST-ORG-001',
      officialName: 'Test Organization Limited',
      contactNumber: '9876543210',
      address: 'Test Address, Test City',
      panNumber: 'TESTPAN001',
      poc: {
        name: 'Test POC',
        email: '<EMAIL>',
        designation: 'Manager'
      },
      placeName: 'Test City',
      location: {
        latitude: 28.6139,
        longitude: 77.2090
      }
    });

    // Create test individual
    const testIndividual = await Individual.create({
      personalDetails: {
        name: 'Test Individual',
        gender: 'Male',
        dateOfBirth: new Date('1990-01-01'),
        email: '<EMAIL>',
        contactNumber: '9876543211',
        address: 'Test Individual Address',
        stateOrCity: 'Test State',
        panNumber: 'TESTIND001',
        adharNumber: '123456789012'
      },
      placeName: 'Test Individual City',
      location: {
        latitude: 28.7041,
        longitude: 77.1025
      }
    });

    // Create login credentials
    const hashedPassword = await bcrypt.hash('testpass123', 10);
    
    await OrganizationLogin.create({
      orgCode: 'TEST-ORG-001',
      username: 'testorg',
      password: hashedPassword,
      role: 'organization'
    });

    await OrganizationLogin.create({
      orgCode: 'TESTIND001',
      username: 'testind',
      password: hashedPassword,
      role: 'individual'
    });

    // Create test drones for organization
    const orgDrones = await Drone.create([
      {
        droneId: 'TEST-DRONE-001',
        model: 'Test Model A',
        type: 'Surveillance',
        status: 'Active',
        currentLocation: { latitude: 28.6139, longitude: 77.2090, altitude: 100 },
        batteryLevel: 85,
        organizationId: testOrg._id
      },
      {
        droneId: 'TEST-DRONE-002',
        model: 'Test Model B',
        type: 'Mapping',
        status: 'crashed',
        currentLocation: { latitude: 28.6200, longitude: 77.2100, altitude: 0 },
        batteryLevel: 0,
        organizationId: testOrg._id
      },
      {
        droneId: 'TEST-DRONE-003',
        model: 'Test Model C',
        type: 'Other',
        status: 'Maintenance',
        currentLocation: { latitude: 28.6150, longitude: 77.2080, altitude: 0 },
        batteryLevel: 45,
        organizationId: testOrg._id
      }
    ]);

    // Create test drones for individual
    const indDrones = await Drone.create([
      {
        droneId: 'TEST-DRONE-IND-001',
        model: 'Individual Model A',
        type: 'Surveillance',
        status: 'Active',
        currentLocation: { latitude: 28.7041, longitude: 77.1025, altitude: 120 },
        batteryLevel: 90,
        individualId: testIndividual._id
      }
    ]);

    // Create test missions for organization
    await Mission.create([
      {
        missionId: 'TEST-MISSION-001',
        droneId: orgDrones[0]._id,
        organizationId: testOrg._id,
        missionType: 'Spraying',
        status: 'Completed',
        startTime: new Date('2024-01-15T10:00:00Z'),
        endTime: new Date('2024-01-15T12:00:00Z'),
        location: {
          startPoint: { latitude: 28.6139, longitude: 77.2090 },
          endPoint: { latitude: 28.6200, longitude: 77.2100 },
          state: 'Delhi',
          district: 'New Delhi'
        },
        sprayingDetails: {
          areaSprayedHectares: 5.5,
          cropType: 'Wheat',
          pesticide: { name: 'Test Pesticide', quantity: 10, unit: 'liters' }
        }
      },
      {
        missionId: 'TEST-MISSION-002',
        droneId: orgDrones[0]._id,
        organizationId: testOrg._id,
        missionType: 'Spraying',
        status: 'Completed',
        startTime: new Date('2024-02-20T09:00:00Z'),
        endTime: new Date('2024-02-20T11:30:00Z'),
        location: {
          startPoint: { latitude: 28.6150, longitude: 77.2080 },
          endPoint: { latitude: 28.6180, longitude: 77.2120 },
          state: 'Haryana',
          district: 'Gurgaon'
        },
        sprayingDetails: {
          areaSprayedHectares: 8.2,
          cropType: 'Rice',
          pesticide: { name: 'Test Pesticide 2', quantity: 15, unit: 'liters' }
        }
      }
    ]);

    // Create test missions for individual
    await Mission.create([
      {
        missionId: 'TEST-MISSION-IND-001',
        droneId: indDrones[0]._id,
        individualId: testIndividual._id,
        missionType: 'Spraying',
        status: 'Completed',
        startTime: new Date('2024-03-10T08:00:00Z'),
        endTime: new Date('2024-03-10T10:00:00Z'),
        location: {
          startPoint: { latitude: 28.7041, longitude: 77.1025 },
          endPoint: { latitude: 28.7080, longitude: 77.1060 },
          state: 'Delhi',
          district: 'North Delhi'
        },
        sprayingDetails: {
          areaSprayedHectares: 3.0,
          cropType: 'Corn',
          pesticide: { name: 'Individual Pesticide', quantity: 5, unit: 'liters' }
        }
      }
    ]);

    console.log('✅ Test data created successfully');
    console.log('📊 Test Organization ID:', testOrg._id);
    console.log('👤 Test Individual ID:', testIndividual._id);
    console.log('🚁 Organization Drones:', orgDrones.length);
    console.log('🚁 Individual Drones:', indDrones.length);
    
    return {
      organization: testOrg,
      individual: testIndividual,
      orgDrones,
      indDrones
    };

  } catch (error) {
    console.error('❌ Error creating test data:', error);
    throw error;
  }
};

// Test the dashboard endpoints
const testDashboardEndpoints = async () => {
  console.log('\n🧪 Testing Dashboard Endpoints...');
  
  // Note: In a real test, you would make HTTP requests to the endpoints
  // For this demonstration, we'll test the controller functions directly
  
  const { getDroneStatistics, getLiveDroneTracking, getSprayedHectaresOverTime, getAreaSprayedByState } = 
    require('./Controllers/organizationDashboardController');
  
  // Mock request and response objects
  const createMockReq = (orgCode) => ({
    organization: { orgCode },
    query: {}
  });
  
  const createMockRes = () => {
    const res = {};
    res.status = (code) => {
      res.statusCode = code;
      return res;
    };
    res.json = (data) => {
      res.data = data;
      return res;
    };
    return res;
  };

  try {
    // Test organization endpoints
    console.log('\n📊 Testing Organization Dashboard...');
    
    const orgReq = createMockReq('TEST-ORG-001');
    const orgRes = createMockRes();
    
    await getDroneStatistics(orgReq, orgRes);
    console.log('✅ Drone Statistics:', orgRes.data);
    
    await getLiveDroneTracking(orgReq, orgRes);
    console.log('✅ Live Tracking:', orgRes.data?.data?.length, 'drones');
    
    await getSprayedHectaresOverTime(orgReq, orgRes);
    console.log('✅ Sprayed Hectares:', orgRes.data);
    
    await getAreaSprayedByState(orgReq, orgRes);
    console.log('✅ Area by State:', orgRes.data);

    // Test individual endpoints
    console.log('\n👤 Testing Individual Dashboard...');
    
    const indReq = createMockReq('TESTIND001');
    const indRes = createMockRes();
    
    await getDroneStatistics(indReq, indRes);
    console.log('✅ Individual Drone Statistics:', indRes.data);
    
    await getLiveDroneTracking(indReq, indRes);
    console.log('✅ Individual Live Tracking:', indRes.data?.data?.length, 'drones');

  } catch (error) {
    console.error('❌ Error testing endpoints:', error);
  }
};

// Main test function
const runTests = async () => {
  try {
    await connectDB();
    await createTestData();
    await testDashboardEndpoints();
    
    console.log('\n✅ All tests completed successfully!');
    console.log('\n📋 API Endpoints Available:');
    console.log('GET /organization/dashboard/drone-stats');
    console.log('GET /organization/dashboard/live-tracking?filter=active|crashed|flying|all');
    console.log('GET /organization/dashboard/sprayed-hectares?year=2024');
    console.log('GET /organization/dashboard/area-by-state?year=2024');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
};

// Run tests if this file is executed directly
if (require.main === module) {
  runTests();
}

module.exports = { runTests, createTestData };

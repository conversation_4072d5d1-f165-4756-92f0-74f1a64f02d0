const OrganizationLogin = require('../models/organizationLoginModel');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

// Organization/Individual login controller
exports.loginOrganization = async (req, res) => {
  const { username, password, orgCode } = req.body;

  try {
    // Find the login credentials for the org/individual
    const user = await OrganizationLogin.findOne({ username, orgCode });
    if (!user || !user.password) {
      return res.status(401).json({ success: false, message: 'Invalid username, org code, or password' });
    }

    // Compare password
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(401).json({ success: false, message: 'Invalid password' });
    }

    // Generate JWT token
    const token = jwt.sign(
      { id: user._id, username: user.username, orgCode: user.orgCode },
      process.env.JWT_SECRET,
      { expiresIn: '7d' }
    );

    res.json({
      success: true,
      message: 'Login successful',
      token,
      user: {
        username: user.username,
        orgCode: user.orgCode,
        _id: user._id
      }
    });
  } catch (error) {
    console.error('❌ Organization/Individual login error:', error);
    res.status(500).json({ success: false, message: 'Server error during login' });
  }
};

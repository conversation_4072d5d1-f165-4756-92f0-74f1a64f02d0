const Admin=require('../models/Admin')
const bcrypt = require('bcryptjs');
const Individual = require('../models/IndividualRegistration');
const LoginCredential = require('../models/organizationLoginModel');
const generateCredentials = require('../utils/generateCredentials');
const sendEmail = require('../utils/sendEmail');


const generateToken =require('../utils/generateToken')



// Admin login

exports.loginAdmin = async (req, res) => {
  const { email, password } = req.body;

  const admin = await Admin.findOne({ email });

  if (admin && (await bcrypt.compare(password, admin.password))) {
    res.json({
      _id: admin._id,
      email: admin.email,
      token: generateToken(admin._id),
    });
  } else {
    res.status(401).json({ message: 'Invalid credentials' });
  }
};


// @desc    Get admin profile
// @route   GET /api/admin/profile
exports.getAdminProfile = async (req, res) => {
  res.json(req.admin);
};

exports.registerIndividual = async (req, res) => {
  console.log("✅ registerIndividual called");
  try {
    const {
      personalDetails,
      kycDetails,
         location,
      placeName // <-- Accept placeName from request
    } = req.body;

    if (!personalDetails || !personalDetails.name || !personalDetails.email) {
      return res.status(400).json({
        success: false,
        message: 'Name and Email are required.'
      });
    }

    // Check for duplicate Aadhaar or PAN
    const duplicate = await Individual.findOne({
      $or: [
        { 'personalDetails.adharNumber': personalDetails.adharNumber },
        { 'personalDetails.panNumber': personalDetails.panNumber }
      ]
    });

    if (duplicate) {
      return res.status(400).json({
        success: false,
        message: 'Individual already registered with this Aadhaar or PAN.'
      });
    }

    // Save Individual
    const newIndividual = await Individual.create({
      personalDetails,
      kycDetails,
      location,
      placeName // <-- Save placeName
    });

    // Generate login credentials
    const { username, password } = generateCredentials(personalDetails.name);
    const hashedPassword = await bcrypt.hash(password, 10);
    const orgCode = personalDetails.panNumber; // You can use adharNumber too

    // Save to LoginCredential
    await LoginCredential.create({
      username,
      password: hashedPassword,
      orgCode,
      role: 'individual'
    });

    // Email message
    const message = `
      <h3>Welcome to Shakti, ${personalDetails.name}</h3>
      <p>Your registration is successful.</p>
      <p>Login Credentials:</p>
      <ul>
        <li><strong>Username:</strong> ${username}</li>
        <li><strong>Password:</strong> ${password}</li>
        <li><strong>Org Code:</strong> ${orgCode}</li>
      </ul>
      <p>Please do not share these credentials with anyone.</p>
    `;

    await sendEmail({
      to: personalDetails.email,
      subject: 'Shakti Individual Login Credentials',
      html: message,
      text: ''
    });

    res.status(201).json({
      success: true,
      message: 'Individual registered and login credentials sent via email.',
      individual: newIndividual
    });

  } catch (err) {
    console.error('❌ Error registering individual:', err);
    res.status(500).json({ success: false, message: 'Server error during individual registration.' });
  }
};

// @desc    Change admin password
// @route   PUT /api/admin/password
exports.updateAdminPassword = async (req, res) => {
  const { oldPassword, newPassword } = req.body;
  const admin = await Admin.findById(req.admin._id);

  const isMatch = await bcrypt.compare(oldPassword, admin.password);
  if (!isMatch) {
    return res.status(400).json({ message: 'Old password is incorrect' });
  }

  admin.password = await bcrypt.hash(newPassword, 10);
  await admin.save();

  res.json({ message: 'Password updated successfully' });
};
const Drone = require('../models/Drone');

// Get all drones
exports.getAllDrones = async (req, res) => {
  try {
    const drones = await Drone.find().populate('organizationId', 'orgName');
    res.status(200).json({ drones });
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch drones' });
  }
};

// Lock a drone
exports.lockDrone = async (req, res) => {
  try {
    const drone = await Drone.findByIdAndUpdate(
      req.params.id,
      { isLocked: true },
      { new: true }
    );
    if (!drone) return res.status(404).json({ error: 'Drone not found' });
    res.status(200).json({ message: 'Drone locked', drone });
  } catch (error) {
    res.status(500).json({ error: 'Failed to lock drone' });
  }
};

// Unlock a drone
exports.unlockDrone = async (req, res) => {
  try {
    const drone = await Drone.findByIdAndUpdate(
      req.params.id,
      { isLocked: false },
      { new: true }
    );
    if (!drone) return res.status(404).json({ error: 'Drone not found' });
    res.status(200).json({ message: 'Drone unlocked', drone });
  } catch (error) {
    res.status(500).json({ error: 'Failed to unlock drone' });
  }
};

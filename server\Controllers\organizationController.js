const Organization = require('../models/organizationModel');
const OrganizationLogin = require('../models/organizationLoginModel');  // shared model for both individual and organization 
const sendEmail = require('../utils/sendEmail');
const generateCredentials = require('../utils/generateCredentials');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// registering the organization from admin

exports.registerOrganization= async (req, res) => {
  try {
    const {
      orgName,
      orgType,
      registrationNumber,
      officialName,
      contactNumber,
      address,
      panNumber,
      poc,
      authorizationForm,
      location,
      placeName // <-- Accept placeName from request
    } = req.body;

    // Check for duplicates
    const existingOrg = await Organization.findOne({
      $or: [
        { registrationNumber },
        { "poc.email": poc?.email }
      ]
    });

    if (existingOrg) {
      return res.status(400).json({
        success: false,
        message: 'An organization with this registration number or email already exists.'
      });
    }

    // Create organization
    const newOrg = await Organization.create({
      orgName,
      orgType,
      registrationNumber,
      officialName,
      contactNumber,
      address,
      panNumber,
      poc,
      authorizationForm,
      location,
      placeName // <-- Save placeName
    });

    // Generate login credentials
    const { username, password } = generateCredentials(poc.name);
    const hashedPassword = await bcrypt.hash(password, 10);

    // Save credentials to login collection
    await OrganizationLogin.create({
      orgCode: registrationNumber,
      username,
      password: hashedPassword
    });

    // Send credentials via email
    const message = `
      <h3>Welcome to Shakti, ${poc.name}</h3>
      <p>Your organization has been successfully registered.</p>
      <p>Here are your login credentials:</p>
      <ul>
        <li><strong>Org Code:</strong> ${registrationNumber}</li>
        <li><strong>Username:</strong> ${username}</li>
        <li><strong>Password:</strong> ${password}</li>
      </ul>
      <p>Please keep these credentials safe.</p>
    `;

    await sendEmail({
      to: poc.email,
      subject: 'Shakti Organization Login Credentials',
      html: message,
      text: ''
    });

    res.status(201).json({
      success: true,
      message: 'Organization registered and credentials sent via email.',
      organization: newOrg
    });
  } catch (error) {
    console.error('❌ Registration error:', error);
    res.status(500).json({ success: false, message: 'Server error during registration.' });
  }
};

// Get organization profile
exports.getOrganizationProfile = async (req, res) => {
  try {
    const orgCode = req.organization.orgCode;

    // Find organization by orgCode
    const organization = await Organization.findOne({ registrationNumber: orgCode });

    if (!organization) {
      return res.status(404).json({
        success: false,
        message: 'Organization not found'
      });
    }

    res.status(200).json({
      success: true,
      organization
    });
  } catch (error) {
    console.error('❌ Get profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching profile.'
    });
  }
};

// Update organization profile
exports.updateOrganizationProfile = async (req, res) => {
  try {
    const orgCode = req.organization.orgCode;
    const updateData = req.body;

    // Find and update organization
    const updatedOrganization = await Organization.findOneAndUpdate(
      { registrationNumber: orgCode },
      updateData,
      { new: true, runValidators: true }
    );

    if (!updatedOrganization) {
      return res.status(404).json({
        success: false,
        message: 'Organization not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Organization profile updated successfully',
      organization: updatedOrganization
    });
  } catch (error) {
    console.error('❌ Update profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating profile.'
    });
  }
};


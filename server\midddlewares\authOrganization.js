const jwt = require('jsonwebtoken');
const OrganizationLogin = require('../models/organizationLoginModel');

// Middleware to protect organization/individual routes
const protectOrganization = async (req, res, next) => {
  let token;

  if (
    req.headers.authorization &&
    req.headers.authorization.startsWith('Bearer')
  ) {
    try {
      token = req.headers.authorization.split(' ')[1];
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const user = await OrganizationLogin.findById(decoded.id).select('-password');

      if (!user) {
        return res.status(404).json({ message: 'Organization/Individual not found' });
      }

      req.organization = user;
      next();
    } catch (error) {
      return res.status(401).json({ message: 'Invalid token' });
    }
  } else {
    return res.status(401).json({ message: 'Not authorized, no token' });
  }
};

module.exports = { protectOrganization };

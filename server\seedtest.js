// seedAdmin.js

const mongoose = require('mongoose');
const dotenv = require('dotenv');
const bcrypt = require('bcryptjs');
const Admin = require('./models/Admin'); // Adjust path if needed

dotenv.config();

const seedAdmin = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('MongoDB connected');

    // Delete all existing admin users
    await Admin.deleteMany({});
    console.log('Old admin data deleted');

    // Hash the password
    const hashedPassword = await bcrypt.hash('1234', 10);

    // Create new admin
    const admin = new Admin({
      email: '<EMAIL>',
      password: hashedPassword,
    });

    await admin.save();
    console.log('Admin seeded successfully');

    process.exit();
  } catch (error) {
    console.error('Error seeding admin:', error);
    process.exit(1);
  }
};

seedAdmin();

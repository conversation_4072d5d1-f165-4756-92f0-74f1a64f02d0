const express = require('express');
const { loginOrganization } = require('../Controllers/organizationLogin');
const { getOrganizationProfile, updateOrganizationProfile } = require('../Controllers/organizationController');
const { protectOrganization } = require('../midddlewares/authOrganization');

const router = express.Router();

// Organization/Individual login route
router.post('/login', loginOrganization);

// Get organization/individual profile
router.get('/profile', protectOrganization, getOrganizationProfile);

// Update organization/individual profile
router.put('/profile', protectOrganization, updateOrganizationProfile);

module.exports = router;

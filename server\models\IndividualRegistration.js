const mongoose = require('mongoose');

const individualSchema = new mongoose.Schema({
  personalDetails: {
    name: String,
    gender: String,
    dateOfBirth: Date,
    email: String,
    contactNumber: String,
    alternateContact: String,
    address: String,
    stateOrCity: String,
    panNumber: String,
    adharNumber: String
  },
  kycDetails: {
    kycDocument: String,      // File path or base64
    idProof: String
  },
  placeName: { type: String },
  location: {
    latitude: { type: Number },
    longitude: { type: Number }
  }
}, { timestamps: true });

module.exports = mongoose.model('IndividualRegistration', individualSchema);

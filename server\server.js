const express = require('express');
const mongoose = require('mongoose');
const dotenv = require('dotenv');
const cors = require('cors');
const adminRoutes = require('./Routes/adminRoutes');
const organizationRoutes = require('./Routes/organizationRoutes');
const organizationDashboardRoutes = require('./Routes/organizationDashboardRoutes');
// If adminRoutes already includes registerOrganization, no need for separate file

dotenv.config();

const app = express();
app.use(cors());
app.use(express.json());

app.use('/admin', adminRoutes); // This includes register-organization
app.use('/organization', organizationRoutes); // Organization/Individual login route
app.use('/organization/dashboard', organizationDashboardRoutes); // Organization dashboard routes

// Connect DB here (if missing)
const db = require('./config/db');
db(); // check you’re calling the function to connect

app.listen(process.env.PORT || 5000, () => {
  console.log(`Server running on port ${process.env.PORT || 5000}`);
});

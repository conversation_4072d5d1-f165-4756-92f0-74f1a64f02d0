const WebSocket = require('ws');

const wss = new WebSocket.Server({ port: 5000 });
console.log('Fake WebSocket server running at ws://localhost:5000');

wss.on('connection', (ws) => {
  console.log('Client connected to fake WebSocket server');

  // Send fake data every 2 seconds
  const interval = setInterval(() => {
    const fakeData = {
      device_id: "FAKE-DRONE-001",
      accelX: Math.random() * 2 - 1,
      accelY: Math.random() * 2 - 1,
      accelZ: Math.random() * 2 - 1,
      altitude: Math.floor(Math.random() * 100),
      bmpTemp: 25 + Math.random() * 5,
      latitude: 12.9716 + Math.random() * 0.01,
      longitude: 77.5946 + Math.random() * 0.01,
      pitch: Math.random() * 90,
      roll: Math.random() * 90,
      yaw: Math.random() * 180,
      pressure: 900 + Math.random() * 100,
      rtcTime: new Date().toISOString()
    };

    ws.send(JSON.stringify(fakeData));
  }, 2000);

  ws.on('close', () => {
    clearInterval(interval);
    console.log('Client disconnected');
  });
});

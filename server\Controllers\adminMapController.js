const Drone = require('../models/Drone');
const Individual = require('../models/IndividualRegistration');                //  Import your  models
const Organization = require('../models/organizationModel');

// dashboard map controller

exports.getAllDronesForMap = async (req, res) => {
  try {
    const drones = await Drone.find({}, 'droneId status currentLocation'); // Only needed fields
    res.json(drones);
  } catch (err) {
    console.error('Error fetching drones for map:', err);
    res.status(500).json({ error: 'Server error' });
  }
};




exports.getAllOrgindLocations = async (req, res) => {
  try {
    // Fetch organizations with placeName
    const organizations = await Organization.find(
      { placeName: { $exists: true, $ne: null } },
      'orgName registrationNumber placeName poc orgType'
    );

    // Fetch individuals with placeName
    const individuals = await Individual.find(
      { placeName: { $exists: true, $ne: null } },
      'personalDetails placeName'
    );

    // Format organization data
    const formattedOrganizations = organizations.map(org => ({
      name: org.orgName,
      code: org.registrationNumber,
      placeName: org.placeName,
      type: 'Organization',
      subtype: org.orgType,
      contactPerson: org.poc?.name || 'N/A'
    }));

    // Format individual data
    const formattedIndividuals = individuals.map(ind => ({
      name: ind.personalDetails.name,
      code: ind.personalDetails.panNumber || 'N/A',
      placeName: ind.placeName,
      type: 'Individual',
      contactPerson: ind.personalDetails.name
    }));

    // Combine both
    const combinedList = [...formattedOrganizations, ...formattedIndividuals];

    res.status(200).json({
      success: true,
      header: "Organizations / Individuals List",
      data: combinedList
    });
  } catch (error) {
    console.error('❌ Error fetching organization/individual locations:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
};

// for the list of drones 
exports.getEntityWithDrones = async (req, res) => {
  try {
    const entityId = req.params.id;

    // Try to find as an organization first
    let entity = await Organization.findById(entityId).select('orgName location');
    let entityType = 'Organization';

    // If not found, try as individual
    if (!entity) {
      entity = await Individual.findById(entityId).select('personalDetails location');
      entityType = 'Individual';
    }

    if (!entity) {
      return res.status(404).json({ message: 'Organization or Individual not found' });
    }

    // Determine the correct drone filter key
    const droneFilter = entityType === 'Organization'
      ? { organizationId: entityId }
      : { individualId: entityId };

    const drones = await Drone.find(droneFilter).select('droneId status currentLocation');

    // Format entity response
    const formattedEntity =
      entityType === 'Organization'
        ? {
            name: entity.orgName,
            type: 'Organization',
            location: entity.location
          }
        : {
            name: entity.personalDetails.name,
            type: 'Individual',
            location: entity.location
          };

    res.status(200).json({
      entity: formattedEntity,
      drones
    });
  } catch (error) {
    console.error('❌ Error fetching entity and drones:', error);
    res.status(500).json({ message: 'Server Error' });
  }
};

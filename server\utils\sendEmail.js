const nodemailer = require('nodemailer');
require('dotenv').config();

const sendEmail = async ({ to, subject, html, text = '' }) => {
  console.log('📨 Preparing to send email...');

  const transporter = nodemailer.createTransport({
    host: process.env.SMTP_HOST,
    port: Number(process.env.SMTP_PORT),
    secure: false, // false for port 587
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS
    }
  });

  const mailOptions = {
    from: `"Shakti Admin" <${process.env.SMTP_USER}>`,
    to,
    subject,
    text,
    html
  };

  try {
    const info = await transporter.sendMail(mailOptions);
    console.log(`📤 Email sent: ${info.messageId} to ${to}`);
  } catch (error) {
    console.error('❌ Failed to send email:', error);
    throw error;
  }
};

module.exports = sendEmail;

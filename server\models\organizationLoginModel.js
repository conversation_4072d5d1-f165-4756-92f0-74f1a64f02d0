const mongoose = require('mongoose');

const orgLoginSchema = new mongoose.Schema({
  orgCode: { type: String, required: true, unique: true },  // registrationNumber
  username: { type: String, required: true },
  password: { type: String, required: true }, // hashed
  role: { type: String, enum: ['organization', 'individual'], default: 'organization' }
}, { timestamps: true });

module.exports = mongoose.model('OrganizationLogin', orgLoginSchema);

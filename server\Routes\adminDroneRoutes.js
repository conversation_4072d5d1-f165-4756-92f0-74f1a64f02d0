const express = require('express');
const router = express.Router();
const auth = require('../midddlewares/auth');
const Drone = require('../models/Drone');
const Organization=require('../models/organizationModel')
const {
  getAllDrones,
  lockDrone,
  unlockDrone
} = require('../Controllers/adminDroneController');
const { protectAdmin } = require('../midddlewares/authMiddleware');


// Admin gets all drones
router.get('/drones', protectAdmin, getAllDrones);

// Admin locks/unlocks drones
router.patch('/drones/:id/lock',protectAdmin, lockDrone);
router.patch('/drones/:id/unlock',protectAdmin, unlockDrone);



// @route   GET /api/admin/dashboard-stats
// @desc    Get dashboard statistics
// @access  Private
router.get('/dashboard-stats', protectAdmin, async (req, res) => {
  try {
    const totalDrones = await Drone.countDocuments();
    const inventoryDrones = await Drone.countDocuments({ status: 'inventory' });
    const deployedDrones = await Drone.countDocuments({ status: 'deployed' });
    const maintenanceDrones = await Drone.countDocuments({ status: 'maintenance' });
    const registeredOrganizations = await Organization.countDocuments();

    return res.status(200).json({
      totalDrones,
      deployedDrones,
      maintenanceDrones,
      registeredOrganizations
    });
  } catch (error) {
    console.error('Dashboard stats error:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;

const WebSocket = require('ws');

// 1. Connect to external WebSocket
const EXTERNAL_SOCKET_URL = 'ws://localhost:5000'; // <-- updated to local fake server
const externalSocket = new WebSocket(EXTERNAL_SOCKET_URL);

// 2. Create WebSocket server for frontend
const wss = new WebSocket.Server({ port: 4001 });
let frontendClients = [];

wss.on('connection', (ws) => {
  console.log('Frontend connected');
  frontendClients.push(ws);

  ws.on('close', () => {
    frontendClients = frontendClients.filter(c => c !== ws);
  });
});

// 3. Handle incoming drone data
externalSocket.on('message', (message) => {
  try {
    const data = JSON.parse(message);

    // Basic status determination
    const status = determineDroneStatus(data);

    const filteredData = {
      droneId: data.device_id,
      location: {
        latitude: data.latitude,
        longitude: data.longitude,
        altitude: data.altitude,
      },
      orientation: {
        pitch: data.pitch,
        roll: data.roll,
        yaw: data.yaw,
      },
      acceleration: {
        x: data.accelX,
        y: data.accelY,
        z: data.accelZ,
      },
      temperature: data.bmpTemp,
      pressure: data.pressure,
      time: data.rtcTime,
      status, // active, inactive, crashed
    };

    // 4. Broadcast to frontend
    frontendClients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(JSON.stringify(filteredData));
      }
    });

  } catch (err) {
    console.error('Invalid data format:', err.message);
  }
});

externalSocket.on('open', () => console.log('Connected to external WebSocket'));
externalSocket.on('error', err => console.error('WebSocket error:', err.message));

// Optional: status logic
function determineDroneStatus(data) {
  const { accelX, accelY, accelZ, latitude, longitude } = data;

  const isStationary = Math.abs(accelX) < 0.1 && Math.abs(accelY) < 0.1 && Math.abs(accelZ) < 0.1;
  const isCrashed = latitude === 0 && longitude === 0 && isStationary;

  if (isCrashed) return 'crashed';
  if (isStationary) return 'inactive';
  return 'active';
}

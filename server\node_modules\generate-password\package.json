{"name": "generate-password", "version": "1.7.1", "description": "Easy library for generating unique passwords.", "main": "main.js", "exports": {"types": "./src/generate.d.ts", "default": "./main.js"}, "types": "src/generate.d.ts", "scripts": {"test": "./node_modules/.bin/mocha", "coverage": "sh ./test/coverage.sh", "lint": "./node_modules/.bin/eslint src test main.js example.js"}, "repository": {"type": "git", "url": "https://github.com/brendanashworth/generate-password"}, "keywords": ["generate", "password", "generator", "unique"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/brendanashworth/generate-password/issues"}, "homepage": "https://github.com/brendanashworth/generate-password", "devDependencies": {"chai": "^1.10.0", "codecov": "^3.8.3", "eslint": "^6.5.1", "jscover": "^0.1.1", "mocha": "^7.0.0", "mocha-lcov-reporter": "^1.2.0", "underscore": "^1.7.0"}}
<!DOCTYPE html>
<html>
  <body>
    <h2>Drone WebSocket Test</h2>
    <pre id="output"></pre>

    <script>
      const socket = new WebSocket('ws://localhost:4001');
      const output = document.getElementById('output');

      socket.onopen = () => output.innerText += "Connected to relay WebSocket...\n";

      socket.onmessage = (event) => {
        const data = JSON.parse(event.data);
        output.innerText += JSON.stringify(data, null, 2) + "\n\n";
      };

      socket.onerror = (err) => output.innerText += "Error: " + err.message;
    </script>
  </body>
</html>

const Drone = require('../models/Drone');
const Mission = require('../models/Mission');
const Organization = require('../models/organizationModel');
const Individual = require('../models/IndividualRegistration');

// Section 1: Get drone statistics for organization dashboard
exports.getDroneStatistics = async (req, res) => {
  try {
    const orgCode = req.organization.orgCode;
    
    // Find organization by orgCode to get the organization ID
    let organizationId = null;
    let individualId = null;
    
    const organization = await Organization.findOne({ registrationNumber: orgCode });
    if (organization) {
      organizationId = organization._id;
    } else {
      // Check if it's an individual
      const individual = await Individual.findOne({ 'personalDetails.panNumber': orgCode });
      if (individual) {
        individualId = individual._id;
      } else {
        return res.status(404).json({
          success: false,
          message: 'Organization/Individual not found'
        });
      }
    }

    // Build query based on whether it's organization or individual
    const droneQuery = organizationId 
      ? { organizationId: organizationId }
      : { individualId: individualId };

    // Get drone counts by status
    const totalDrones = await Drone.countDocuments(droneQuery);
    const activeDrones = await Drone.countDocuments({ ...droneQuery, status: 'Active' });
    const crashedDrones = await Drone.countDocuments({ ...droneQuery, status: 'crashed' });
    const maintenanceDrones = await Drone.countDocuments({ ...droneQuery, status: 'Maintenance' });

    res.status(200).json({
      success: true,
      data: {
        totalDrones,
        activeDrones,
        crashedDrones,
        maintenanceDrones
      }
    });
  } catch (error) {
    console.error('❌ Error fetching drone statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching drone statistics'
    });
  }
};

// Section 2: Get live drone tracking data with filtering
exports.getLiveDroneTracking = async (req, res) => {
  try {
    const orgCode = req.organization.orgCode;
    const { filter } = req.query; // active, crashed, flying, all
    
    // Find organization by orgCode to get the organization ID
    let organizationId = null;
    let individualId = null;
    
    const organization = await Organization.findOne({ registrationNumber: orgCode });
    if (organization) {
      organizationId = organization._id;
    } else {
      const individual = await Individual.findOne({ 'personalDetails.panNumber': orgCode });
      if (individual) {
        individualId = individual._id;
      } else {
        return res.status(404).json({
          success: false,
          message: 'Organization/Individual not found'
        });
      }
    }

    // Build base query
    let droneQuery = organizationId 
      ? { organizationId: organizationId }
      : { individualId: individualId };

    // Apply filter
    if (filter && filter !== 'all') {
      if (filter === 'flying') {
        // Consider drones as flying if they have recent activity (within last 30 minutes)
        const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);
        droneQuery.lastSeen = { $gte: thirtyMinutesAgo };
        droneQuery.status = 'Active';
      } else {
        droneQuery.status = filter === 'active' ? 'Active' : 'crashed';
      }
    }

    const drones = await Drone.find(droneQuery)
      .select('droneId model status currentLocation batteryLevel lastSeen')
      .sort({ lastSeen: -1 });

    res.status(200).json({
      success: true,
      data: drones
    });
  } catch (error) {
    console.error('❌ Error fetching live drone tracking:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching live drone tracking'
    });
  }
};

// Section 3: Get sprayed hectares over time (yearly graph)
exports.getSprayedHectaresOverTime = async (req, res) => {
  try {
    const orgCode = req.organization.orgCode;
    const { year } = req.query;
    const targetYear = year ? parseInt(year) : new Date().getFullYear();
    
    // Find organization by orgCode to get the organization ID
    let organizationId = null;
    let individualId = null;
    
    const organization = await Organization.findOne({ registrationNumber: orgCode });
    if (organization) {
      organizationId = organization._id;
    } else {
      const individual = await Individual.findOne({ 'personalDetails.panNumber': orgCode });
      if (individual) {
        individualId = individual._id;
      } else {
        return res.status(404).json({
          success: false,
          message: 'Organization/Individual not found'
        });
      }
    }

    // Build query for missions
    const missionQuery = {
      ...(organizationId ? { organizationId } : { individualId }),
      missionType: 'Spraying',
      status: 'Completed',
      createdAt: {
        $gte: new Date(`${targetYear}-01-01`),
        $lt: new Date(`${targetYear + 1}-01-01`)
      }
    };

    const monthlyData = await Mission.aggregate([
      { $match: missionQuery },
      {
        $group: {
          _id: { $month: '$createdAt' },
          totalHectares: { $sum: '$sprayingDetails.areaSprayedHectares' }
        }
      },
      { $sort: { '_id': 1 } }
    ]);

    // Fill missing months with 0
    const monthlyHectares = Array(12).fill(0);
    monthlyData.forEach(({ _id, totalHectares }) => {
      monthlyHectares[_id - 1] = totalHectares || 0;
    });

    res.status(200).json({
      success: true,
      data: {
        year: targetYear,
        monthlyHectares
      }
    });
  } catch (error) {
    console.error('❌ Error fetching sprayed hectares over time:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching sprayed hectares data'
    });
  }
};

// Section 3: Get area sprayed by state (pie chart data)
exports.getAreaSprayedByState = async (req, res) => {
  try {
    const orgCode = req.organization.orgCode;
    const { year } = req.query;
    const targetYear = year ? parseInt(year) : new Date().getFullYear();
    
    // Find organization by orgCode to get the organization ID
    let organizationId = null;
    let individualId = null;
    
    const organization = await Organization.findOne({ registrationNumber: orgCode });
    if (organization) {
      organizationId = organization._id;
    } else {
      const individual = await Individual.findOne({ 'personalDetails.panNumber': orgCode });
      if (individual) {
        individualId = individual._id;
      } else {
        return res.status(404).json({
          success: false,
          message: 'Organization/Individual not found'
        });
      }
    }

    // Build query for missions
    const missionQuery = {
      ...(organizationId ? { organizationId } : { individualId }),
      missionType: 'Spraying',
      status: 'Completed',
      createdAt: {
        $gte: new Date(`${targetYear}-01-01`),
        $lt: new Date(`${targetYear + 1}-01-01`)
      }
    };

    const stateData = await Mission.aggregate([
      { $match: missionQuery },
      {
        $group: {
          _id: '$location.state',
          totalHectares: { $sum: '$sprayingDetails.areaSprayedHectares' }
        }
      },
      { $sort: { totalHectares: -1 } }
    ]);

    res.status(200).json({
      success: true,
      data: {
        year: targetYear,
        stateData: stateData.map(item => ({
          state: item._id,
          hectares: item.totalHectares || 0
        }))
      }
    });
  } catch (error) {
    console.error('❌ Error fetching area sprayed by state:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching area sprayed by state data'
    });
  }
};

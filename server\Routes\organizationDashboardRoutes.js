const express = require('express');
const router = express.Router();
const { protectOrganization } = require('../midddlewares/authOrganization');
const {
  getDroneStatistics,
  getLiveDroneTracking,
  getSprayedHectaresOverTime,
  getAreaSprayedByState
} = require('../Controllers/organizationDashboardController');

// Section 1: Drone Statistics Routes
// GET /organization/dashboard/drone-stats
// Returns: { totalDrones, activeDrones, crashedDrones, maintenanceDrones }
router.get('/drone-stats', protectOrganization, getDroneStatistics);

// Section 2: Live Drone Tracking Routes
// GET /organization/dashboard/live-tracking?filter=active|crashed|flying|all
// Returns: Array of drones with current location and status
router.get('/live-tracking', protectOrganization, getLiveDroneTracking);

// Section 3: Analytics Routes
// GET /organization/dashboard/sprayed-hectares?year=2024
// Returns: Monthly data of sprayed hectares for the year
router.get('/sprayed-hectares', protectOrganization, getSprayedHectaresOverTime);

// GET /organization/dashboard/area-by-state?year=2024
// Returns: Pie chart data showing area sprayed by state
router.get('/area-by-state', protectOrganization, getAreaSprayedByState);

module.exports = router;

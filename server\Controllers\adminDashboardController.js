const Organization = require('../models/organizationModel');
const mongoose = require('mongoose');

exports.getMonthlyOrganizationStats = async (req, res) => {
  try {
    const currentYear = new Date().getFullYear();

    const stats = await Organization.aggregate([
      {
        $match: {
          createdAt: {
            $gte: new Date(`${currentYear}-01-01`),
            $lte: new Date(`${currentYear}-12-31`)
          }
        }
      },
      {
        $group: {
          _id: { $month: "$createdAt" },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { "_id": 1 }
      }
    ]);

    // Fill missing months with 0
    const monthlyData = Array(12).fill(0);
    stats.forEach(({ _id, count }) => {
      monthlyData[_id - 1] = count;
    });

    res.json({ year: currentYear, monthlyRegistrations: monthlyData });
  } catch (err) {
    console.error("Error fetching organization stats", err);
    res.status(500).json({ message: "Server error" });
  }
};
// controllers/adminDashboardController.js

const Drone = require('../models/Drone');

exports.getDroneStatsPerMonth = async (req, res) => {
  try {
    const currentYear = new Date().getFullYear();
    const inventory = Array(12).fill(0);
    const deployed = Array(12).fill(0);
    const carryOver = Array(12).fill(0);

    // Get all drones created this year
    const drones = await Drone.find({
      createdAt: {
        $gte: new Date(`${currentYear}-01-01`),
        $lt: new Date(`${currentYear + 1}-01-01`)
      }
    });

    // Track added and deployed per month
    drones.forEach(drone => {
      const month = drone.createdAt.getMonth(); // 0 - 11

      inventory[month] += 1;

      if (drone.organizationId) {
        const deployedMonth = (drone.updatedAt || drone.createdAt).getMonth(); // fallback
        deployed[deployedMonth] += 1;
      }
    });

    // Apply carry-over logic
    const actualInventory = [];
    let runningInventory = 0;

    for (let i = 0; i < 12; i++) {
      runningInventory += inventory[i];      // add new drones
      runningInventory -= deployed[i];       // subtract deployed drones
      if (runningInventory < 0) runningInventory = 0;
      actualInventory[i] = runningInventory; // current month's inventory
    }

    return res.json({
      year: currentYear,
      months: [
        'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
        'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
      ],
      inventory: actualInventory,
      deployed
    });
  } catch (error) {
    console.error('Error fetching drone stats:', error);
    return res.status(500).json({ error: 'Server error' });
  }
};


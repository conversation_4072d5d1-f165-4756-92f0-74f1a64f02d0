{"dependencies": {"axios": "^1.10.0", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "generate-password": "^1.7.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.1", "multer": "^2.0.1", "nodemailer": "^7.0.3", "nodemon": "^3.1.10", "ws": "^8.18.3"}, "name": "server", "version": "1.0.0", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "dev": "nodemon server.js"}, "author": "", "license": "ISC", "description": ""}
# Organization Dashboard API Documentation

This document describes the backend API endpoints for the organization/individual dashboard functionality.

## Authentication

All dashboard endpoints require authentication using the organization/individual JWT token.

**Header Required:**
```
Authorization: Bearer <jwt_token>
```

## Base URL

All dashboard endpoints are prefixed with `/organization/dashboard/`

---

## Section 1: Drone Statistics

### GET `/organization/dashboard/drone-stats`

Returns the four container statistics for the dashboard.

**Response:**
```json
{
  "success": true,
  "data": {
    "totalDrones": 10,
    "activeDrones": 7,
    "crashedDrones": 2,
    "maintenanceDrones": 1
  }
}
```

**Description:**
- `totalDrones`: Total number of drones owned by the organization/individual
- `activeDrones`: Number of drones with status "Active"
- `crashedDrones`: Number of drones with status "crashed"
- `maintenanceDrones`: Number of drones with status "Maintenance"

---

## Section 2: Live Drone Tracking

### GET `/organization/dashboard/live-tracking`

Returns live tracking data for all drones with optional filtering.

**Query Parameters:**
- `filter` (optional): Filter drones by status
  - `active`: Only active drones
  - `crashed`: Only crashed drones
  - `flying`: Only drones that are currently flying (active with recent activity)
  - `all`: All drones (default)

**Example Request:**
```
GET /organization/dashboard/live-tracking?filter=active
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "_id": "drone_id",
      "droneId": "DRONE-001",
      "model": "DJI Phantom 4",
      "status": "Active",
      "currentLocation": {
        "latitude": 28.6139,
        "longitude": 77.2090,
        "altitude": 100
      },
      "batteryLevel": 85,
      "lastSeen": "2024-01-15T10:30:00Z"
    }
  ]
}
```

---

## Section 3: Analytics

### GET `/organization/dashboard/sprayed-hectares`

Returns monthly data showing sprayed hectares over time for a specific year.

**Query Parameters:**
- `year` (optional): Year to get data for (default: current year)

**Example Request:**
```
GET /organization/dashboard/sprayed-hectares?year=2024
```

**Response:**
```json
{
  "success": true,
  "data": {
    "year": 2024,
    "monthlyHectares": [0, 15.5, 23.2, 18.7, 0, 0, 0, 0, 0, 0, 0, 0]
  }
}
```

**Description:**
- `monthlyHectares`: Array of 12 numbers representing hectares sprayed each month (Jan-Dec)

### GET `/organization/dashboard/area-by-state`

Returns pie chart data showing area sprayed by state for a specific year.

**Query Parameters:**
- `year` (optional): Year to get data for (default: current year)

**Example Request:**
```
GET /organization/dashboard/area-by-state?year=2024
```

**Response:**
```json
{
  "success": true,
  "data": {
    "year": 2024,
    "stateData": [
      {
        "state": "Punjab",
        "hectares": 45.8
      },
      {
        "state": "Haryana",
        "hectares": 32.1
      },
      {
        "state": "Delhi",
        "hectares": 12.3
      }
    ]
  }
}
```

---

## Error Responses

All endpoints may return the following error responses:

### 401 Unauthorized
```json
{
  "message": "Not authorized, no token"
}
```

### 404 Not Found
```json
{
  "success": false,
  "message": "Organization/Individual not found"
}
```

### 500 Server Error
```json
{
  "success": false,
  "message": "Server error while fetching data"
}
```

---

## Data Models

### Mission Model
The dashboard analytics are based on the Mission model which tracks:
- Spraying missions with area covered
- Location data including state information
- Mission status and completion dates
- Drone and organization/individual associations

### Drone Model
The drone statistics and tracking are based on the Drone model which includes:
- Drone status (Active, crashed, Maintenance, inactive)
- Current location and battery level
- Organization or individual ownership
- Last seen timestamp

---

## Testing

To test the dashboard APIs:

1. Run the test script:
```bash
node test-dashboard.js
```

2. This will create sample data and test all endpoints

3. Use the created test credentials to make actual HTTP requests:
   - Organization: username: `testorg`, orgCode: `TEST-ORG-001`, password: `testpass123`
   - Individual: username: `testind`, orgCode: `TESTIND001`, password: `testpass123`

---

## Notes

- The system supports both organizations and individuals with the same functionality
- Drone ownership is determined by either `organizationId` or `individualId` in the Drone model
- Mission data is used for analytics and must have `status: 'Completed'` to be included in statistics
- Live tracking considers drones "flying" if they have been active within the last 30 minutes

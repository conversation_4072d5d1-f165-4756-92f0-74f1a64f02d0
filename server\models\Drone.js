const mongoose = require('mongoose');

const droneSchema = new mongoose.Schema({
  droneId: {
    type: String,
    required: true,
    unique: true
  },
  model: {
    type: String,
    required: true
  },
  type: {
    type: String,
    enum: ['Surveillance', 'Delivery', 'Mapping', 'Other'],
    required: true
  },
  status: {
    type: String,
    enum: ['Active', 'crashed', 'Maintenance', 'inactive'],
    default: 'Active'
  },
  currentLocation: {
    latitude: {
      type: Number,
      required: true
    },
    longitude: {
      type: Number,
      required: true
    },
    altitude: {
      type: Number,
      default: 0
    }
  },
  batteryLevel: {
    type: Number,
    min: 0,
    max: 100
  },
  organizationId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'OrganizationRegistration',
    required: false
  },
  individualId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'IndividualRegistration',
    required: false
  },
  lastSeen: {
    type: Date,
    default: Date.now
  },
  logs: [
    {
      timestamp: Date,
      event: String,
      location: {
        lat: Number,
        lng: Number,
        alt: Number
      }
    }
  ],// models/Drone.js
isLocked: {
  type: Boolean,
  default: false
}

}, { timestamps: true });

// Validation to ensure either organizationId or individualId is provided
droneSchema.pre('save', function(next) {
  if (!this.organizationId && !this.individualId) {
    return next(new Error('Either organizationId or individualId must be provided'));
  }
  if (this.organizationId && this.individualId) {
    return next(new Error('Cannot have both organizationId and individualId'));
  }
  next();
});

module.exports = mongoose.model('Drone', droneSchema);
